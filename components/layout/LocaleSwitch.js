"use client";
import { Link } from "@/i18n/navigation";
import { useLocale } from "next-intl";
import { usePathname } from "next/navigation";
import { useState, useTransition } from "react";

export default function LocaleSwitch() {
  const locale = useLocale();
  const pathname = usePathname();
  const [isPending, startTransition] = useTransition();
  const [isOpen, setIsOpen] = useState(false);

  const items = [
    { value: "en", label: "ENG" },
    { value: "ru", label: "RUS" },
    { value: "th", label: "THA" },
    { value: "ch", label: "CHN" },
  ];

  const currentItem = items.find((item) => item.value === locale) || items[0];

  const handleLocaleChange = (newLocale) => {
    if (newLocale === locale) return;

    startTransition(() => {
      setIsOpen(false);
    });
  };

  return (
    <div className="main-header__language-switcher">
      <div className="icon">
        <span className="fa fa-globe"></span>
      </div>
      <div className="language-switcher clearfix">
        <div className="select-box clearfix">
          <div className="select-box clearfix">
            <div
              className="selectmenu wide"
              onClick={() => setIsOpen(!isOpen)}
              style={{
                cursor: isPending ? "wait" : "pointer",
              }}
            >
              {currentItem.label}
              <span className={`dropdown-arrow ${isOpen ? "open" : ""}`}>
                ▼
              </span>
            </div>
            {isOpen && (
              <div className="dropdown-options">
                {items.map((item) => (
                  <Link
                    key={item.value}
                    href={pathname}
                    locale={item.value}
                    className={`dropdown-option ${
                      item.value === locale ? "active" : ""
                    }`}
                    onClick={() => handleLocaleChange(item.value)}
                  >
                    {item.label}
                  </Link>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
